"use client";

import { useRef, useState, useEffect } from 'react';
import AnimatedFixedTitle from './AnimatedFixedTitle';

const Process = () => {
  const sectionRef = useRef(null);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [titleVisible, setTitleVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      
      // Element's top position relative to viewport
      const elementTop = rect.top;
      
      // Trigger point: when element is 50% visible
      const triggerPoint = windowHeight * 0.3;
      
      // Show animation when scrolling down and element comes into view
      if (elementTop <= triggerPoint && !hasAnimatedIn) {
        setHasAnimatedIn(true);
        setTitleVisible(true);
      }
      
      // Hide animation when scrolling back up past the same trigger point
      if (elementTop > triggerPoint && hasAnimatedIn) {
        setHasAnimatedIn(false);
        setTitleVisible(false);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn]);

  return (
    <>
      {/* Fixed/Centered Title */}
      <AnimatedFixedTitle
        title="Process"
        titleVisible={titleVisible}
        scrollEffects={{ opacity: 1, blur: 0, scale: 1 }}
        className="font-heading font-extrabold text-primary text-4xl lg:text-6xl"
        containerClassName="fixed inset-0 flex items-center justify-center pointer-events-none z-5"
      />

      {/* Process Section */}
      <section 
        ref={sectionRef}
        className="bg-background py-20 min-h-screen"
      >
        <div className="w-full mx-auto px-6">
          <div className="h-screen flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-2xl text-primary mb-4">Working with Me</h2>
              <p className="text-secondary">
                This is where the overlapping cards will show the process of working together.
              </p>
              <p className="text-secondary mt-4">
                For now, this is a placeholder to test the scroll transition from Projects.
              </p>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Process;
