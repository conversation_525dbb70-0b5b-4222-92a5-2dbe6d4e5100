"use client";

import { motion } from 'framer-motion';

const ProcessCard = ({ scrollProgress, processSteps }) => {
  // Calculate which step we're currently showing
  const stepCount = processSteps.length;

  // For now, with just one step, show it when scroll progress starts
  const currentStep = processSteps[0];

  // Calculate card position based on scroll progress - START IMMEDIATELY LIKE PROJECT CARDS
  // Cards should start appearing as soon as Process title starts fading (scrollProgress > 0)
  // Start from just below viewport (80vh) and move to center (50vh)
  const startPositionVh = 80;  // Start just below viewport (visible through feathered mask)
  const endPositionVh = 50;    // End at center of viewport

  // Card appears immediately when scroll progress starts (like project cards)
  // Make movement more aggressive in the beginning
  const cardProgress = Math.min(1, scrollProgress * 1.5); // Faster initial movement
  const cardPositionVh = startPositionVh - (cardProgress * (startPositionVh - endPositionVh));

  // Opacity appears immediately when scroll starts
  const cardOpacity = scrollProgress > 0 ? 1 : 0;

  return (
    <div className="w-full h-full relative flex items-center justify-center">
      {/* Process Card */}
      <motion.div
        className="absolute bg-primary/10 backdrop-blur-sm rounded-2xl border border-primary/20 shadow-lg p-8"
        style={{
          opacity: cardOpacity,
          transform: `translateY(${cardPositionVh}vh)`,
          // Responsive sizing based on viewport - smaller than project cards
          width: 'min(300px, 25vw)', // Responsive width, max 300px
          height: 'min(200px, 20vh)', // Responsive height, max 200px
          zIndex: 1
        }}
        initial={{ opacity: 0, y: '80vh' }}
        animate={{
          opacity: cardOpacity,
          y: `${cardPositionVh}vh`
        }}
        transition={{
          duration: 0.2,
          ease: "easeOut"
        }}
      >
        {/* Card Content */}
        <div className="h-full flex flex-col justify-center text-center">
          {/* Step Number */}
          <div className="text-primary text-4xl font-bold mb-2">
            {currentStep.number}
          </div>
          
          {/* Step Title */}
          <h3 className="text-foreground text-lg font-semibold mb-3">
            {currentStep.title}
          </h3>
          
          {/* Step Description */}
          <p className="text-secondary text-sm leading-relaxed">
            {currentStep.description}
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default ProcessCard;
