"use client";

import { motion } from 'framer-motion';

const ProcessCard = ({ scrollProgress, processSteps }) => {
  // Calculate which step we're currently showing
  const stepCount = processSteps.length;
  
  // For now, with just one step, show it when scroll progress starts
  const currentStep = processSteps[0];
  
  // Calculate card position based on scroll progress
  // Card starts from bottom (100vh) and moves to center (50vh)
  const startPosition = 100; // Start from bottom of viewport
  const endPosition = 50;    // End at center of viewport
  
  // Card appears when scroll progress begins and moves to center
  const cardProgress = Math.min(1, scrollProgress * 2); // Make it move faster initially
  const cardPosition = startPosition - (cardProgress * (startPosition - endPosition));
  
  // Opacity increases as card moves up
  const cardOpacity = Math.min(1, scrollProgress * 3);

  return (
    <div className="w-full h-full relative flex items-center justify-center">
      {/* Process Card */}
      <motion.div
        className="absolute bg-primary/10 backdrop-blur-sm rounded-2xl border border-primary/20 shadow-lg p-8"
        style={{
          opacity: cardOpacity,
          transform: `translateY(${cardPosition}vh)`,
          width: '300px',
          height: '200px',
          zIndex: 1
        }}
        initial={{ opacity: 0, y: 100 }}
        animate={{ 
          opacity: cardOpacity, 
          y: `${cardPosition}vh` 
        }}
        transition={{ 
          duration: 0.1, 
          ease: "easeOut" 
        }}
      >
        {/* Card Content */}
        <div className="h-full flex flex-col justify-center text-center">
          {/* Step Number */}
          <div className="text-primary text-4xl font-bold mb-2">
            {currentStep.number}
          </div>
          
          {/* Step Title */}
          <h3 className="text-foreground text-lg font-semibold mb-3">
            {currentStep.title}
          </h3>
          
          {/* Step Description */}
          <p className="text-secondary text-sm leading-relaxed">
            {currentStep.description}
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default ProcessCard;
